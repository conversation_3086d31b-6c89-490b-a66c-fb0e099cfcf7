# Gemini 项目分析总结

## 项目概述

这是一个全栈的AI聊天机器人Web应用。其核心功能是作为一个智能客服，能够理解用户在多轮对话中提出的问题，并从一个预定义的FAQ知识库中找到最合适的答案。

- **前端 (`frontend/`)**: 一个基于 **React** 的单页应用（SPA），提供了用户与聊天机器人交互的界面。用户可以发送消息，查看对话历史，并能看到AI返回的多个候选答案及其置信度。
- **后端 (`backend_host/`)**: 一个基于 **Python** 和 **FastAPI** 的API服务。它不直接生成答案，而是作为一个**代理和编排层**，连接了多个第三方AI平台（如百炼、Coze、火山方舟）。
- **部署 (`docker/`)**: 整个应用被设计为可以通过 **Docker** 和 **Docker Compose** 进行容器化部署，前后端分离，易于扩展和维护。

## 核心业务逻辑：FAQ筛选器Agent

后端最核心的组件是 `faq_filter_agent`。这是一个精心设计的AI Agent，其工作流程如下：

1.  **查询重写 (Query Rewrite)**: 首先，Agent会分析用户的多轮对话历史，将用户最新的、可能不完整的提问，重写成一个清晰、独立的查询语句。
2.  **问题分类 (Classification)**: 接着，Agent利用一个大语言模型（LLM）和预设的FAQ目录，对重写后的查询进行分类，找到它在FAQ知识库中最可能对应的类别。
3.  **答案检索 (Retrieval)**: 根据分类结果，从`faq_doc.json`等文件中精确查找并提取出标准答案。
4.  **向量召回 (Fallback)**: 如果问题分类失败（即LLM无法确定类别），系统会自动切换到**向量搜索**模式，在向量数据库中通过语义相似度来查找相关的FAQ条目，作为一种补充和降级策略。
5.  **重排序 (Reranking)**: （可选）对检索到的多个候选答案，使用专门的重排序模型进行打分和排序，以提高首个答案的准确率。
6.  **返回结果**: 最终，将一个或多个候选答案连同分类路径、置信度分数等信息返回给前端。

## 技术栈总结

-   **前端**:
    -   框架: **React**
    -   HTTP客户端: **Axios**
    -   构建工具: **Create React App**
    -   路由: **React Router**
    -   包管理器: **npm**
-   **后端**:
    -   框架: **FastAPI**
    -   包和环境管理: **uv**
    -   语言: **Python 3.11**
    -   核心库: `Pydantic`, `httpx`
-   **AI/LLM集成**:
    -   支持平台: **百炼 (Bailian), Coze, 火山引擎 (Volcano)**
    -   能力: 文本生成、向量嵌入、语义检索、重排序
-   **部署与运维**:
    -   **Docker** & **Docker Compose**
    -   Web服务器 (前端): **Nginx**

## 项目状态与潜在工作

-   **前端**: `REFACTOR_PROGRESS.md` 文件表明前端最近经历了一次重构。下一步计划是实现一个**批量测试**功能。
-   **后端**: 后端架构清晰，模块化程度高，易于扩展。
-   **数据管理**: FAQ知识库通过`JSON`文件管理，并提供了从`Excel`转换的脚本。
