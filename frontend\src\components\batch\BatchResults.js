import React, { useState } from 'react';

const ResultRow = ({ item, index }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getStatusIcon = () => {
    if (item.status === 'success') return <span className="status-icon success">✅</span>;
    if (item.status === 'failed') return <span className="status-icon failed">❌</span>;
    return <span className="status-icon">❓</span>;
  };

  const mainAnswer = item.result?.response_body?.[0]?.content || 'N/A';
  const score = item.result?.response_body?.[0]?.score;
  const confidence = score !== null && score !== undefined ? `${(score * 100).toFixed(2)}%` : 'N/A';
  const requestId = item.requestId || 'N/A';
  const attemptCount = item.attemptCount || 1;

  return (
    <div className={`result-row ${item.status}`}>
      <div className="result-summary" onClick={() => setIsExpanded(!isExpanded)}>
        <div className="summary-left">
          <span className="row-index">{index + 1}.</span>
          {getStatusIcon()}
          <div className="qa-block">
            <p className="question" title={`Q: ${item.question}\nx-request-id: ${requestId}`}>
              <strong>Q:</strong> {item.question}
            </p>
            <p className="answer" title={`A: ${mainAnswer}`}>
              <strong>A:</strong> {mainAnswer}
            </p>
          </div>
        </div>
        <div className="summary-right">
          <span className="confidence-score">{confidence}</span>
          {attemptCount > 1 && (
            <span className="retry-info" title={`经过${attemptCount}次尝试${item.status === 'success' ? '成功' : '失败'}`}>
              🔄{attemptCount}
            </span>
          )}
          <button className="expand-btn">{isExpanded ? '收起' : '展开'}</button>
        </div>
      </div>
      {isExpanded && (
        <div className="result-details">
          <pre>{JSON.stringify(item, null, 2)}</pre>
        </div>
      )}
    </div>
  );

};

function BatchResults({ results }) {
  return (
    <div className="batch-results-container">
      <h4>4. 测试结果</h4>
      <div className="results-list">
        {results.map((item, index) => (
          <ResultRow key={item.id || index} item={item} index={index} />
        ))}
      </div>
    </div>
  );
}

export default BatchResults;
