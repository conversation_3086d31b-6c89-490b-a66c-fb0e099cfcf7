.service-selector-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.service-selector-wrapper label {
  font-weight: 500;
  color: #34495e;
}

.service-selector {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
}

.service-selector:disabled {
  background-color: #f2f2f2;
  cursor: not-allowed;
}

/* 当在 .config-item 中使用 compact 模式时，与其他输入框保持一致 */
.config-item .service-selector {
  flex: 1; /* 占据剩余空间，与 .config-item input 保持一致 */
}
