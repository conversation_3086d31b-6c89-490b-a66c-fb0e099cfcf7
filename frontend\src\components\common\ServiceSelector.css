.service-selector-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.service-selector-wrapper label {
  width: 120px; /* 与 .config-item label 保持一致 */
  font-weight: 500;
  color: #34495e;
}

.service-selector {
  flex: 1; /* 与 .config-item input 保持一致，占据剩余空间 */
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
}

.service-selector:disabled {
  background-color: #f2f2f2;
  cursor: not-allowed;
}
