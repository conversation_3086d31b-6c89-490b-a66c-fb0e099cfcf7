import React from 'react';
import './ServiceSelector.css';

/**
 * 一个可复用的服务选择下���框组件
 * @param {Object} props
 * @param {string} props.serviceName - 当前选中的服务值
 * @param {Function} props.onServiceChange - 服务变化时的回调函数
 * @param {boolean} props.disabled - 是否禁用
 * @param {string} [props.displayMode='default'] - 显示模式: 'default' 或 'compact'
 * @param {string} [props.className] - 允许外部传入额外的className
 */
function ServiceSelector({ serviceName, onServiceChange, disabled, displayMode = 'default', className = '' }) {
  
  const selectElement = (
    <select
      className={`service-selector ${className}`} // 允许外部class覆盖
      value={serviceName}
      onChange={(e) => onServiceChange(e.target.value)}
      disabled={disabled}
    >
      <option value="volcano">Agent(火山方舟)</option>
      <option value="bailian">Agent(阿里百炼)</option>
    </select>
  );

  if (displayMode === 'compact') {
    return selectElement;
  }

  // 默认模式，带label和wrapper
  return (
    <div className="service-selector-wrapper">
      <label>服务选择:</label>
      {selectElement}
    </div>
  );
}

export default ServiceSelector;

