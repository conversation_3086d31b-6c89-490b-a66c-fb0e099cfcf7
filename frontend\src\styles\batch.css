/* === 全局布局 === */
.batch-test-container {
  padding: 20px;
  background-color: #f7f8fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  max-width: 1200px; /* 设定最大宽度 */
  margin: 0 auto; /* 水平居中 */
  box-sizing: border-box; /* 确保 padding 不会影响总宽度 */
}

.batch-header {
  text-align: center;
  margin-bottom: 24px;
}

.batch-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #2c3e50;
}

.batch-header p {
  font-size: 1rem;
  color: #5a6876;
}

.batch-content {
  /* max-width 和 margin 已移至父容器 */
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

/* === 配置区域 === */
.batch-config-area {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 24px;
}

.file-upload-container, .config-panel {
  flex: 1;
  padding: 20px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background-color: #fdfdfd;
}

.file-upload-container h4, .config-panel h4 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

/* 文件上传 */
.dropzone {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;
}
.dropzone.active, .dropzone:hover {
  border-color: #007bff;
}
.file-name {
  margin-top: 10px;
  font-weight: 500;
  color: #007bff;
}
.error-message {
  margin-top: 10px;
  color: #e74c3c;
  font-weight: 500;
}

/* 全局配置 */
.config-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.config-item label {
  width: 120px;
  font-weight: 500;
  color: #34495e;
}
.config-item input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

/* === 控制按钮 === */
.batch-controls {
  text-align: center;
  margin-bottom: 24px;
}
.batch-controls button {
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  color: #fff;
  background-color: #007bff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s;
}
.batch-controls button:hover {
  background-color: #0056b3;
}
.batch-controls button:disabled {
  background-color: #a0c7e4;
  cursor: not-allowed;
}

/* === 进度和结果区域 === */
.batch-processing-area, .batch-results-area {
  margin-top: 24px;
  padding: 20px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

/* 进度条 */
.batch-progress-container h4 {
  margin-top: 0;
}
.progress-bar-container {
  width: 100%;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin: 10px 0;
}
.progress-bar {
  height: 20px;
  line-height: 20px;
  text-align: center;
  color: white;
  background-color: #28a745;
  transition: width 0.4s ease;
}
.progress-stats {
  display: flex;
  gap: 20px;
  font-weight: 500;
}
.success-text { color: #28a745; }
.failed-text { color: #dc3545; }

/* 结果列表 */
.batch-results-container h4 {
  margin-top: 0;
}
.results-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.result-row {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  overflow: hidden;
}
.result-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  cursor: pointer;
  background-color: #f8f9fa;
}
.result-summary:hover {
  background-color: #f1f3f5;
}
.summary-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-grow: 1; /* 允许其占据可用空间 */
  flex-shrink: 1; /* 允许其收缩 */
  min-width: 0; /* 关键属性，使得内部元素的 text-overflow 生效 */
}

.summary-right {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0; /* 防止右侧元素被压缩 */
}

.qa-block {
  display: flex;
  flex-direction: column;
  min-width: 0; /* 允许此块收缩 */
}

.status-icon { font-size: 1.2rem; }

.question, .answer {
  margin: 0;
  white-space: nowrap; /* 保持单行显示 */
  overflow: hidden; /* 隐藏溢出部分 */
  text-overflow: ellipsis; /* 使用省略号表示溢出 */
}
.expand-btn {
  padding: 4px 8px;
  font-size: 0.8rem;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
}
.result-details {
  padding: 12px;
  background-color: #fff;
  border-top: 1px solid #e9ecef;
}
.result-details pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  background: #2c3e50;
  color: #f8f8f2;
  padding: 10px;
  border-radius: 4px;
} 