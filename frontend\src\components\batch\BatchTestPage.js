import React, { useState } from 'react';
import axios from 'axios';
import '../../styles/batch.css';
import FileUpload from './FileUpload';
import BatchProgress from './BatchProgress';
import BatchResults from './BatchResults';
import ServiceSelector from '../common/ServiceSelector'; // 导入公共组件
import CONFIG from '../../config';
import { generateRequestIdWithPrefix } from '../../utils/requestId';

function BatchTestPage() {
  // 页面核心状态
  const [testCases, setTestCases] = useState([]);
  const [results, setResults] = useState([]);
  const [progress, setProgress] = useState(0);
  const [progressStats, setProgressStats] = useState({ completed: 0, total: 0, success: 0, failed: 0 });
  const [isProcessing, setIsProcessing] = useState(false);

  // 全局配置状态
  const [concurrency, setConcurrency] = useState(3);
  const [timeout, setTimeout] = useState(30);
  const [retries, setRetries] = useState(1);
  const [serviceName, setServiceName] = useState('volcano');

  // 文件解析回调
  const handleFileParsed = (data) => {
    console.log("文件解析完成:", data);
    setTestCases(data);
    setResults([]);
    setProgress(0);
    setProgressStats({ completed: 0, total: data.length, success: 0, failed: 0 });
  };

  // 进度更新回调
  const handleProgress = ({ completed, total, success, failed }) => {
    setProgress(total > 0 ? (completed / total) * 100 : 0);
    setProgressStats({ completed, total, success, failed });
  };

  
  /**
   * 批量处理测试用例
   */
  const processBatch = async (testCases, config, onProgress) => {
    const { concurrency, timeout, retries, service } = config;
    const total = testCases.length;
    let completed = 0;
    let success = 0;
    let failed = 0;
    const results = [];
    const queue = [...testCases];

    const API_URL = `${CONFIG.API_BASE_URL}/chat/faq_filter`;

    const updateProgress = () => {
      onProgress({ completed, total, success, failed });
    };

    const processTestCase = async (testCase) => {
      const requestId = generateRequestIdWithPrefix('batch');
      const requestBody = {
        conversation: [{ role: 'user', content: testCase.question }],
        service: service,
        context_params: {
          channel_name: testCase.channel || '',
          platform_name: testCase.platform || '',
        },
      };
      
      const requestConfig = {
        headers: {
          'x-request-id': requestId
        },
        timeout: timeout * 1000,
      };

      try {
        const response = await axios.post(API_URL, requestBody, requestConfig);
        success++;
        return { ...testCase, status: 'success', result: response.data, error: null, requestId };
      } catch (error) {
        // TODO: 实现重试逻辑
        failed++;
        return { ...testCase, status: 'failed', result: null, error: error.message, requestId };
      } finally {
        completed++;
        updateProgress();
      }
    };

    const worker = async () => {
      while (queue.length > 0) {
        const testCase = queue.shift();
        if (testCase) {
          const result = await processTestCase(testCase);
          results.push(result);
        }
      }
    };

    const workers = Array(concurrency).fill(null).map(() => worker());
    await Promise.all(workers);

    return results;
  };


  // 开始测试
  const handleStartProcessing = async () => {
    if (testCases.length === 0) {
      alert("请先上传并解析测试文件。");
      return;
    }
    setIsProcessing(true);
    setResults([]);
    setProgress(0);
    setProgressStats({ completed: 0, total: testCases.length, success: 0, failed: 0 });

    const finalResults = await processBatch(
      testCases,
      { concurrency, timeout, retries, service: serviceName },
      handleProgress
    );

    setResults(finalResults);
    setIsProcessing(false);
    console.log("批量处理完成:", finalResults);
  };

  return (
    <div className="batch-test-container">
      <header className="batch-header">
        <h2>批量测试</h2>
        <p>上传Excel文件，对模型进行批量自动化测试。</p>
      </header>
      
      <div className="batch-content">
        <div className="batch-config-area">
          <FileUpload onFileParsed={handleFileParsed} />

          <div className="config-panel">
            <h4>2. 全局配置</h4>
            <div className="config-item">
              {/* 使用公共组件替换原来的select */}
              <ServiceSelector 
                serviceName={serviceName}
                onServiceChange={setServiceName}
                disabled={isProcessing}
              />
            </div>
            <div className="config-item">
              <label htmlFor="concurrency">并发数:</label>
              <input 
                type="number" 
                id="concurrency" 
                value={concurrency} 
                onChange={(e) => setConcurrency(parseInt(e.target.value, 10))}
                min="1"
                disabled={isProcessing}
              />
            </div>
            <div className="config-item">
              <label htmlFor="timeout">超时时间 (秒):</label>
              <input 
                type="number" 
                id="timeout" 
                value={timeout} 
                onChange={(e) => setTimeout(parseInt(e.target.value, 10))}
                min="1"
                disabled={isProcessing}
              />
            </div>
            <div className="config-item">
              <label htmlFor="retries">重试次数:</label>
              <input 
                type="number" 
                id="retries" 
                value={retries} 
                onChange={(e) => setRetries(parseInt(e.target.value, 10))}
                min="0"
                disabled={isProcessing}
              />
            </div>
          </div>
        </div>

        <div className="batch-controls">
          <button 
            onClick={handleStartProcessing} 
            disabled={isProcessing || testCases.length === 0}
          >
            {isProcessing ? `正在测试... ${progress.toFixed(0)}%` : '开始测试'}
          </button>
        </div>

        {(isProcessing || results.length > 0) && (
          <div className="batch-processing-area">
            <BatchProgress progress={progress} stats={progressStats} />
          </div>
        )}

        {results.length > 0 && (
          <div className="batch-results-area">
            <BatchResults results={results} />
          </div>
        )}
      </div>
    </div>
  );
}

export default BatchTestPage; 